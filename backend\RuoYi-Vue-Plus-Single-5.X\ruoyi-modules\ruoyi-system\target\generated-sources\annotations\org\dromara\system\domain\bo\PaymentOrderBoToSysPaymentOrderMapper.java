package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__136;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPaymentOrder;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__136.class,
    uses = {},
    imports = {}
)
public interface PaymentOrderBoToSysPaymentOrderMapper extends BaseMapper<PaymentOrderBo, SysPaymentOrder> {
}
