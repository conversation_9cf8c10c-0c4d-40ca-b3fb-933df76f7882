# SmartInterview AI智能面试系统 - 记忆中枢 v1.0

## 项目概览

**项目名称**: SmartInterview AI智能面试系统  
**项目类型**: AI驱动的智能面试培训平台  
**开发团队**: TTB9  
**技术架构**: 前后端分离 + 多端统一 + AI Agent协同  
**创建时间**: 2025-07-16  
**记忆更新**: 2025-08-01  

## 核心价值主张

基于7个专业AI Agent的智能面试准备生态系统，为求职者提供全方位、个性化的面试培训服务，包括简历优化、技能评估、模拟面试、反馈分析等功能。

## 技术架构

### 后端技术栈
- **框架**: RuoYi-Vue-Plus 5.4.0 (Spring Boot 3.4.6)
- **Java版本**: JDK 17
- **数据库**: MySQL 8.0
- **缓存**: Redis + Redisson 3.45.1
- **认证**: Sa-Token 1.42.0
- **ORM**: MyBatis-Plus 3.5.12
- **工具库**: Hutool 5.8.35
- **消息队列**: RocketMQ (规划中)
- **搜索引擎**: Elasticsearch (规划中)

### 前端技术栈
- **框架**: UniApp + Vue 3.4.21
- **开发语言**: TypeScript 5.7.2
- **构建工具**: Vite 5.2.8
- **包管理**: pnpm
- **CSS框架**: UnoCSS + TailwindCSS 4.1.7
- **UI组件**: wot-design-uni 1.4.0
- **状态管理**: Pinia 2.0.36
- **国际化**: vue-i18n 9.1.9

### AI技术栈 (规划)
- **大语言模型**: 科大讯飞星火大模型
- **自然语言处理**: Transformers + BERT/GPT
- **机器学习**: scikit-learn
- **深度学习**: TensorFlow/PyTorch
- **服务接口**: Python Flask/FastAPI

## 项目结构

### 后端模块结构
```
backend/RuoYi-Vue-Plus-Single-5.X/
├── ruoyi-admin/           # 启动模块
├── ruoyi-common/          # 公共模块
├── ruoyi-extend/          # 扩展模块
└── ruoyi-modules/         # 业务模块
    ├── ruoyi-system/      # 系统管理模块
    └── ruoyi-app/         # 应用业务模块
```

### 前端模块结构
```
front/unibest-main/src/
├── components/            # 公共组件
├── pages/                # 页面目录
├── service/              # API服务
├── store/                # 状态管理
├── types/                # 类型定义
├── utils/                # 工具函数
└── hooks/                # 自定义钩子
```

## 核心业务模块

### 1. 用户管理模块
- **用户注册/登录**: 支持手机号、邮箱、第三方登录
- **用户画像**: 个人信息、技能标签、学习偏好
- **权限管理**: 基于Sa-Token的认证授权体系

### 2. 面试管理模块
- **面试岗位选择**: 多专业分类支持
- **模拟面试**: 实时对话、场景模拟
- **面试记录**: 历史记录、进度跟踪
- **面试结果**: 多维度评估、详细反馈

### 3. 学习资源模块
- **题库管理**: 分类题库、智能推荐
- **知识库**: 文档管理、学习资料
- **视频学习**: 在线观看、进度记录
- **学习分析**: 数据统计、能力评估

### 4. AI Agent模块 (核心)
- **面试官AI**: 智能问题生成、动态追问
- **简历分析师AI**: 简历解析、优化建议
- **技能评估师AI**: 能力测试、技能评分
- **职业顾问AI**: 职业规划、发展建议
- **模拟面试官AI**: 场景模拟、压力测试
- **反馈分析师AI**: 表现分析、改进建议
- **学习指导师AI**: 学习计划、资源推荐

## 数据库设计

### 核心业务表
- `app_user_manage`: 用户管理表
- `app_major`: 专业分类表
- `app_job_category`: 岗位分类表
- `app_question_bank`: 题库表
- `app_question`: 题目表
- `app_interview_result`: 面试结果表
- `app_user_resume`: 用户简历表
- `app_knowledge_base`: 知识库表
- `app_feedback`: 反馈表

### AI相关表 (规划)
- `ai_agent_sessions`: AI会话表
- `ai_conversations`: 对话记录表
- `skill_assessments`: 技能评估表
- `interview_records`: 面试记录表

## 关键特性

### 1. 多端统一
- **Web端**: H5响应式界面
- **小程序**: 微信、支付宝等多平台
- **APP**: Android/iOS原生体验
- **数据同步**: 跨设备学习进度同步

### 2. AI驱动
- **智能对话**: 基于大语言模型的自然交互
- **个性化推荐**: 根据用户画像定制内容
- **实时评估**: 即时反馈和建议
- **多模态分析**: 文本、语音、视频综合分析

### 3. 企业级特性
- **高并发**: 支持1000+用户同时在线
- **高可用**: 99.9%服务可用性
- **安全性**: 数据加密、权限控制
- **可扩展**: 微服务架构、容器化部署

## 开发进度

### 已完成功能
- ✅ 基础框架搭建
- ✅ 用户认证系统
- ✅ 基础业务模块
- ✅ 前端页面框架
- ✅ 数据库设计

### 进行中功能
- 🔄 AI Agent基础框架
- 🔄 面试功能完善
- 🔄 学习资源管理

### 待开发功能
- ⏳ 多模态数据处理
- ⏳ AI分析引擎
- ⏳ 智能推荐系统
- ⏳ 实时音视频处理

## 部署配置

### 开发环境
- **后端端口**: 8080
- **前端端口**: 3000 (开发模式)
- **数据库**: MySQL 8.0
- **缓存**: Redis

### 生产环境 (规划)
- **容器化**: Docker + Kubernetes
- **负载均衡**: Nginx
- **监控**: Prometheus + Grafana
- **日志**: ELK Stack

## 重要决策记录

1. **技术选型**: 选择RuoYi-Vue-Plus作为后端框架，提供企业级稳定性
2. **前端架构**: 采用UniApp实现多端统一，降低开发维护成本
3. **AI集成**: 计划集成科大讯飞星火大模型，提供专业AI能力
4. **数据库设计**: 采用分模块设计，支持业务扩展

## 注意事项

1. **性能要求**: AI响应时间需控制在3秒内
2. **安全考虑**: 用户数据需加密存储，敏感信息脱敏处理
3. **扩展性**: 预留AI Agent扩展接口，支持功能迭代
4. **兼容性**: 确保多端一致性体验

---
**最后更新**: 2025-08-01  
**更新内容**: 初始化项目记忆中枢，记录完整技术架构和业务模块信息
