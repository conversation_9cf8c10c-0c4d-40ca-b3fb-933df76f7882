<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.system.mapper.SysMajorMapper">

    <resultMap type="org.dromara.system.domain.vo.MajorVo" id="MajorResult">
        <result property="majorId"             column="major_id"             />
        <result property="majorCode"           column="major_code"           />
        <result property="majorName"           column="major_name"           />
        <result property="icon"                column="icon"                 />
        <result property="color"               column="color"                />
        <result property="questionBankCount"   column="question_bank_count"  />
        <result property="sort"                column="sort"                 />
        <result property="status"              column="status"               />
        <result property="remark"              column="remark"               />
        <result property="createBy"            column="create_by"            />
        <result property="createTime"          column="create_time"          />
        <result property="updateBy"            column="update_by"            />
        <result property="updateTime"          column="update_time"          />
    </resultMap>

    <sql id="selectMajorVo">
        select major_id, major_code, major_name, icon, color, question_bank_count, sort, status, remark, create_by, create_time, update_by, update_time
        from app_major
    </sql>

    <!-- 根据专业编码查询专业信息 -->
    <select id="selectByMajorCode" parameterType="String" resultMap="MajorResult">
        <include refid="selectMajorVo"/>
        where major_code = #{majorCode}
    </select>

    <!-- 查询启用状态的专业列表 -->
    <select id="selectEnabledMajorList" resultMap="MajorResult">
        <include refid="selectMajorVo"/>
        where status = '0'
        order by sort asc, create_time desc
    </select>

    <!-- 根据专业名称模糊查询 -->
    <select id="selectByMajorNameLike" parameterType="String" resultMap="MajorResult">
        <include refid="selectMajorVo"/>
        where major_name like concat('%', #{majorName}, '%')
        order by sort asc, create_time desc
    </select>

    <!-- 统计专业下的题库数量 -->
    <select id="countQuestionBanksByMajorId" parameterType="Long" resultType="Integer">
        select count(1) from app_question_bank where major_id = #{majorId} and del_flag = '0'
    </select>

    <!-- 批量更新专业状态 -->
    <update id="batchUpdateStatus">
        update app_major set status = #{status}
        where major_id in
        <foreach collection="majorIds" item="majorId" open="(" separator="," close=")">
            #{majorId}
        </foreach>
    </update>

    <!-- 获取最大排序值 -->
    <select id="selectMaxSort" resultType="Integer">
        select max(sort) from app_major
    </select>

</mapper>
