package org.dromara.app.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.vo.VideoDetailVo;
import org.dromara.app.utils.VideoMappingUtils;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T20:50:02+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class VideoToVideoDetailVoMapperImpl implements VideoToVideoDetailVoMapper {

    @Override
    public VideoDetailVo convert(Video arg0) {
        if ( arg0 == null ) {
            return null;
        }

        VideoDetailVo videoDetailVo = new VideoDetailVo();

        videoDetailVo.setCategory( arg0.getCategory() );
        videoDetailVo.setCollectCount( arg0.getCollectCount() );
        if ( arg0.getCreateTime() != null ) {
            videoDetailVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        videoDetailVo.setDescription( arg0.getDescription() );
        videoDetailVo.setDifficulty( arg0.getDifficulty() );
        videoDetailVo.setDuration( arg0.getDuration() );
        videoDetailVo.setFree( VideoMappingUtils.map( arg0.getFree() ) );
        videoDetailVo.setId( arg0.getId() );
        videoDetailVo.setInstructor( arg0.getInstructor() );
        videoDetailVo.setInstructorAvatar( arg0.getInstructorAvatar() );
        videoDetailVo.setInstructorId( arg0.getInstructorId() );
        videoDetailVo.setLikeCount( arg0.getLikeCount() );
        videoDetailVo.setPrice( arg0.getPrice() );
        videoDetailVo.setPublishTime( arg0.getPublishTime() );
        videoDetailVo.setRating( arg0.getRating() );
        videoDetailVo.setShareCount( arg0.getShareCount() );
        videoDetailVo.setStudentCount( arg0.getStudentCount() );
        videoDetailVo.setTags( VideoMappingUtils.map( arg0.getTags() ) );
        videoDetailVo.setThumbnail( arg0.getThumbnail() );
        videoDetailVo.setTitle( arg0.getTitle() );
        if ( arg0.getUpdateTime() != null ) {
            videoDetailVo.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        videoDetailVo.setVideoUrl( arg0.getVideoUrl() );
        videoDetailVo.setViewCount( arg0.getViewCount() );

        return videoDetailVo;
    }

    @Override
    public VideoDetailVo convert(Video arg0, VideoDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCategory( arg0.getCategory() );
        arg1.setCollectCount( arg0.getCollectCount() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setDescription( arg0.getDescription() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setDuration( arg0.getDuration() );
        arg1.setFree( VideoMappingUtils.map( arg0.getFree() ) );
        arg1.setId( arg0.getId() );
        arg1.setInstructor( arg0.getInstructor() );
        arg1.setInstructorAvatar( arg0.getInstructorAvatar() );
        arg1.setInstructorId( arg0.getInstructorId() );
        arg1.setLikeCount( arg0.getLikeCount() );
        arg1.setPrice( arg0.getPrice() );
        arg1.setPublishTime( arg0.getPublishTime() );
        arg1.setRating( arg0.getRating() );
        arg1.setShareCount( arg0.getShareCount() );
        arg1.setStudentCount( arg0.getStudentCount() );
        if ( arg1.getTags() != null ) {
            List<String> list = VideoMappingUtils.map( arg0.getTags() );
            if ( list != null ) {
                arg1.getTags().clear();
                arg1.getTags().addAll( list );
            }
            else {
                arg1.setTags( null );
            }
        }
        else {
            List<String> list = VideoMappingUtils.map( arg0.getTags() );
            if ( list != null ) {
                arg1.setTags( list );
            }
        }
        arg1.setThumbnail( arg0.getThumbnail() );
        arg1.setTitle( arg0.getTitle() );
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( LocalDateTime.ofInstant( arg0.getUpdateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setVideoUrl( arg0.getVideoUrl() );
        arg1.setViewCount( arg0.getViewCount() );

        return arg1;
    }
}
