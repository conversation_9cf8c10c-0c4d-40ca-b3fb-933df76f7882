package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.VideoComment;
import org.dromara.app.domain.VideoCommentToVideoCommentVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {VideoCommentToVideoCommentVoMapper.class,VideoCommentToVideoCommentVoMapper.class},
    imports = {}
)
public interface VideoCommentVoToVideoCommentMapper extends BaseMapper<VideoCommentVo, VideoComment> {
}
