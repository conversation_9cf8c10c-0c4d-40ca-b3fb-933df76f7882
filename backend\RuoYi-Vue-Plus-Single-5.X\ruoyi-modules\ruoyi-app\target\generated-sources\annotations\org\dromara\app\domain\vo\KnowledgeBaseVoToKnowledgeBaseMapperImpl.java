package org.dromara.app.domain.vo;

import java.time.ZoneOffset;
import java.util.Date;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.KnowledgeBase;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T20:50:03+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class KnowledgeBaseVoToKnowledgeBaseMapperImpl implements KnowledgeBaseVoToKnowledgeBaseMapper {

    @Override
    public KnowledgeBase convert(KnowledgeBaseVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        KnowledgeBase knowledgeBase = new KnowledgeBase();

        knowledgeBase.setCreateBy( arg0.getCreateBy() );
        knowledgeBase.setCreateDept( arg0.getCreateDept() );
        if ( arg0.getCreateTime() != null ) {
            knowledgeBase.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        knowledgeBase.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            knowledgeBase.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        knowledgeBase.setDescription( arg0.getDescription() );
        knowledgeBase.setDocumentCount( arg0.getDocumentCount() );
        knowledgeBase.setExtendConfig( arg0.getExtendConfig() );
        knowledgeBase.setId( arg0.getId() );
        knowledgeBase.setIndexConfig( arg0.getIndexConfig() );
        knowledgeBase.setLastSyncTime( arg0.getLastSyncTime() );
        knowledgeBase.setName( arg0.getName() );
        knowledgeBase.setRemark( arg0.getRemark() );
        knowledgeBase.setSortOrder( arg0.getSortOrder() );
        knowledgeBase.setStatus( arg0.getStatus() );
        knowledgeBase.setType( arg0.getType() );
        knowledgeBase.setVectorCount( arg0.getVectorCount() );
        knowledgeBase.setVectorDimension( arg0.getVectorDimension() );

        return knowledgeBase;
    }

    @Override
    public KnowledgeBase convert(KnowledgeBaseVo arg0, KnowledgeBase arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( Date.from( arg0.getCreateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setUpdateBy( arg0.getUpdateBy() );
        if ( arg0.getUpdateTime() != null ) {
            arg1.setUpdateTime( Date.from( arg0.getUpdateTime().toInstant( ZoneOffset.UTC ) ) );
        }
        else {
            arg1.setUpdateTime( null );
        }
        arg1.setDescription( arg0.getDescription() );
        arg1.setDocumentCount( arg0.getDocumentCount() );
        arg1.setExtendConfig( arg0.getExtendConfig() );
        arg1.setId( arg0.getId() );
        arg1.setIndexConfig( arg0.getIndexConfig() );
        arg1.setLastSyncTime( arg0.getLastSyncTime() );
        arg1.setName( arg0.getName() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setType( arg0.getType() );
        arg1.setVectorCount( arg0.getVectorCount() );
        arg1.setVectorDimension( arg0.getVectorDimension() );

        return arg1;
    }
}
