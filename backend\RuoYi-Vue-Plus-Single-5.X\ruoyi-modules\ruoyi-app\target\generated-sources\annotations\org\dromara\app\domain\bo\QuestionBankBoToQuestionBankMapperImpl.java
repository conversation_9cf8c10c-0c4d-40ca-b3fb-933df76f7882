package org.dromara.app.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.QuestionBank;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T20:50:03+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class QuestionBankBoToQuestionBankMapperImpl implements QuestionBankBoToQuestionBankMapper {

    @Override
    public QuestionBank convert(QuestionBankBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        QuestionBank questionBank = new QuestionBank();

        questionBank.setCreateBy( arg0.getCreateBy() );
        questionBank.setCreateDept( arg0.getCreateDept() );
        questionBank.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            questionBank.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        questionBank.setSearchValue( arg0.getSearchValue() );
        questionBank.setUpdateBy( arg0.getUpdateBy() );
        questionBank.setUpdateTime( arg0.getUpdateTime() );
        questionBank.setBankCode( arg0.getBankCode() );
        questionBank.setBankId( arg0.getBankId() );
        questionBank.setCategories( arg0.getCategories() );
        questionBank.setColor( arg0.getColor() );
        questionBank.setDescription( arg0.getDescription() );
        questionBank.setDifficulty( arg0.getDifficulty() );
        questionBank.setIcon( arg0.getIcon() );
        questionBank.setMajorId( arg0.getMajorId() );
        questionBank.setPracticeCount( arg0.getPracticeCount() );
        questionBank.setRemark( arg0.getRemark() );
        questionBank.setSort( arg0.getSort() );
        questionBank.setStatus( arg0.getStatus() );
        questionBank.setTitle( arg0.getTitle() );
        questionBank.setTotalQuestions( arg0.getTotalQuestions() );

        return questionBank;
    }

    @Override
    public QuestionBank convert(QuestionBankBo arg0, QuestionBank arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setBankCode( arg0.getBankCode() );
        arg1.setBankId( arg0.getBankId() );
        arg1.setCategories( arg0.getCategories() );
        arg1.setColor( arg0.getColor() );
        arg1.setDescription( arg0.getDescription() );
        arg1.setDifficulty( arg0.getDifficulty() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setMajorId( arg0.getMajorId() );
        arg1.setPracticeCount( arg0.getPracticeCount() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setTotalQuestions( arg0.getTotalQuestions() );

        return arg1;
    }
}
