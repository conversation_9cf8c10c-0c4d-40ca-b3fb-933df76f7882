package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.UserResume;
import org.dromara.app.domain.UserResumeToUserResumeVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {UserResumeToUserResumeVoMapper.class},
    imports = {}
)
public interface UserResumeVoToUserResumeMapper extends BaseMapper<UserResumeVo, UserResume> {
}
