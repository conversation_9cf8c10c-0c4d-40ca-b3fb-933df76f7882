package io.github.linpeilie;

import org.dromara.app.domain.AppUserToAppUserManageVoMapper;
import org.dromara.app.domain.FeedbackToFeedbackVoMapper;
import org.dromara.app.domain.InterviewModeToInterviewModeVoMapper;
import org.dromara.app.domain.InterviewResultToInterviewResultVoMapper;
import org.dromara.app.domain.JobCategoryToJobCategoryVoMapper;
import org.dromara.app.domain.KnowledgeBaseToKnowledgeBaseVoMapper;
import org.dromara.app.domain.KnowledgeDocumentToKnowledgeDocumentVoMapper;
import org.dromara.app.domain.UserResumeToUserResumeVoMapper;
import org.dromara.app.domain.VideoCommentToVideoCommentVoMapper;
import org.dromara.app.domain.VideoToVideoDetailVoMapper;
import org.dromara.app.domain.bo.AppUserManageBoToAppUserMapper;
import org.dromara.app.domain.bo.FeedbackBoToFeedbackMapper;
import org.dromara.app.domain.bo.InterviewResultBoToInterviewResultMapper;
import org.dromara.app.domain.bo.KnowledgeBaseBoToKnowledgeBaseMapper;
import org.dromara.app.domain.bo.KnowledgeDocumentBoToKnowledgeDocumentMapper;
import org.dromara.app.domain.bo.QuestionBankBoToQuestionBankMapper;
import org.dromara.app.domain.bo.QuestionBoToQuestionMapper;
import org.dromara.app.domain.bo.UserResumeBoToUserResumeMapper;
import org.dromara.app.domain.bo.VideoCommentBoToVideoCommentMapper;
import org.dromara.app.domain.vo.AppUserManageVoToAppUserMapper;
import org.dromara.app.domain.vo.FeedbackVoToFeedbackMapper;
import org.dromara.app.domain.vo.InterviewModeVoToInterviewModeMapper;
import org.dromara.app.domain.vo.InterviewResultVoToInterviewResultMapper;
import org.dromara.app.domain.vo.JobCategoryVoToJobCategoryMapper;
import org.dromara.app.domain.vo.KnowledgeBaseVoToKnowledgeBaseMapper;
import org.dromara.app.domain.vo.KnowledgeDocumentVoToKnowledgeDocumentMapper;
import org.dromara.app.domain.vo.UserResumeVoToUserResumeMapper;
import org.dromara.app.domain.vo.VideoCommentVoToVideoCommentMapper;
import org.dromara.app.domain.vo.VideoDetailVoToVideoMapper;
import org.dromara.system.domain.SysClientToSysClientVoMapper;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper;
import org.dromara.system.domain.SysMajorToMajorVoMapper;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper;
import org.dromara.system.domain.SysNoticeToSysNoticeVoMapper;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper;
import org.dromara.system.domain.SysOssConfigToSysOssConfigVoMapper;
import org.dromara.system.domain.SysOssToSysOssVoMapper;
import org.dromara.system.domain.SysPaymentOrderToPaymentOrderVoMapper;
import org.dromara.system.domain.SysPostToSysPostVoMapper;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper;
import org.dromara.system.domain.SysUserToSysUserVoMapper;
import org.dromara.system.domain.bo.MajorBoToSysMajorMapper;
import org.dromara.system.domain.bo.PaymentOrderBoToSysPaymentOrderMapper;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper;
import org.dromara.system.domain.bo.SysOssBoToSysOssMapper;
import org.dromara.system.domain.bo.SysOssConfigBoToSysOssConfigMapper;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper;
import org.dromara.system.domain.vo.MajorVoToSysMajorMapper;
import org.dromara.system.domain.vo.PaymentOrderVoToSysPaymentOrderMapper;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper;
import org.dromara.system.domain.vo.SysOssConfigVoToSysOssConfigMapper;
import org.dromara.system.domain.vo.SysOssVoToSysOssMapper;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__137.class, SysDictDataVoToSysDictDataMapper.class, AppUserToAppUserManageVoMapper.class, KnowledgeDocumentBoToKnowledgeDocumentMapper.class, SysLogininforVoToSysLogininforMapper.class, SysConfigBoToSysConfigMapper.class, VideoCommentBoToVideoCommentMapper.class, SysPostBoToSysPostMapper.class, SysOssConfigVoToSysOssConfigMapper.class, QuestionBankBoToQuestionBankMapper.class, SysDictDataToSysDictDataVoMapper.class, VideoDetailVoToVideoMapper.class, UserResumeToUserResumeVoMapper.class, InterviewResultVoToInterviewResultMapper.class, SysRoleVoToSysRoleMapper.class, SysClientVoToSysClientMapper.class, SysOssConfigBoToSysOssConfigMapper.class, VideoCommentVoToVideoCommentMapper.class, SysDeptBoToSysDeptMapper.class, SysOssBoToSysOssMapper.class, AppUserManageVoToAppUserMapper.class, SysMenuVoToSysMenuMapper.class, MajorBoToSysMajorMapper.class, KnowledgeBaseToKnowledgeBaseVoMapper.class, FeedbackVoToFeedbackMapper.class, SysClientBoToSysClientMapper.class, SysClientToSysClientVoMapper.class, VideoToVideoDetailVoMapper.class, JobCategoryVoToJobCategoryMapper.class, SysConfigVoToSysConfigMapper.class, PaymentOrderBoToSysPaymentOrderMapper.class, SysSocialBoToSysSocialMapper.class, KnowledgeDocumentToKnowledgeDocumentVoMapper.class, SysOssVoToSysOssMapper.class, InterviewResultBoToInterviewResultMapper.class, SysDictTypeToSysDictTypeVoMapper.class, SysLogininforToSysLogininforVoMapper.class, SysPostVoToSysPostMapper.class, SysSocialToSysSocialVoMapper.class, SysDictTypeBoToSysDictTypeMapper.class, SysMajorToMajorVoMapper.class, VideoCommentToVideoCommentVoMapper.class, SysNoticeBoToSysNoticeMapper.class, SysSocialVoToSysSocialMapper.class, SysOssConfigToSysOssConfigVoMapper.class, SysUserToSysUserVoMapper.class, SysDictTypeVoToSysDictTypeMapper.class, SysMenuBoToSysMenuMapper.class, QuestionBoToQuestionMapper.class, SysConfigToSysConfigVoMapper.class, SysNoticeVoToSysNoticeMapper.class, SysPaymentOrderToPaymentOrderVoMapper.class, PaymentOrderVoToSysPaymentOrderMapper.class, SysMenuToSysMenuVoMapper.class, FeedbackBoToFeedbackMapper.class, SysUserBoToSysUserMapper.class, InterviewModeToInterviewModeVoMapper.class, SysDictDataBoToSysDictDataMapper.class, SysUserVoToSysUserMapper.class, SysNoticeToSysNoticeVoMapper.class, SysOssToSysOssVoMapper.class, KnowledgeDocumentVoToKnowledgeDocumentMapper.class, SysOperLogToSysOperLogVoMapper.class, SysLogininforBoToSysLogininforMapper.class, SysDeptVoToSysDeptMapper.class, KnowledgeBaseVoToKnowledgeBaseMapper.class, UserResumeBoToUserResumeMapper.class, UserResumeVoToUserResumeMapper.class, SysRoleBoToSysRoleMapper.class, InterviewModeVoToInterviewModeMapper.class, SysRoleToSysRoleVoMapper.class, MajorVoToSysMajorMapper.class, JobCategoryToJobCategoryVoMapper.class, InterviewResultToInterviewResultVoMapper.class, KnowledgeBaseBoToKnowledgeBaseMapper.class, AppUserManageBoToAppUserMapper.class, FeedbackToFeedbackVoMapper.class, SysOperLogBoToSysOperLogMapper.class, SysOperLogVoToSysOperLogMapper.class, SysDeptToSysDeptVoMapper.class, SysPostToSysPostVoMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__137 {
}
