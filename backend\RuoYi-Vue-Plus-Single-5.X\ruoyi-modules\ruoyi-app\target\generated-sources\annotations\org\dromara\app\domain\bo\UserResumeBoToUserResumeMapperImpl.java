package org.dromara.app.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.app.domain.UserResume;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T20:50:02+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class UserResumeBoToUserResumeMapperImpl implements UserResumeBoToUserResumeMapper {

    @Override
    public UserResume convert(UserResumeBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        UserResume userResume = new UserResume();

        userResume.setCreateBy( arg0.getCreateBy() );
        userResume.setCreateDept( arg0.getCreateDept() );
        userResume.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            userResume.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        userResume.setSearchValue( arg0.getSearchValue() );
        userResume.setUpdateBy( arg0.getUpdateBy() );
        userResume.setUpdateTime( arg0.getUpdateTime() );
        userResume.setFilePath( arg0.getFilePath() );
        userResume.setFileSize( arg0.getFileSize() );
        userResume.setFileSuffix( arg0.getFileSuffix() );
        userResume.setFileUrl( arg0.getFileUrl() );
        userResume.setIsDefault( arg0.getIsDefault() );
        userResume.setOriginalName( arg0.getOriginalName() );
        userResume.setOssId( arg0.getOssId() );
        userResume.setRemark( arg0.getRemark() );
        userResume.setResumeId( arg0.getResumeId() );
        userResume.setResumeName( arg0.getResumeName() );
        userResume.setStatus( arg0.getStatus() );
        userResume.setUserId( arg0.getUserId() );

        return userResume;
    }

    @Override
    public UserResume convert(UserResumeBo arg0, UserResume arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setFilePath( arg0.getFilePath() );
        arg1.setFileSize( arg0.getFileSize() );
        arg1.setFileSuffix( arg0.getFileSuffix() );
        arg1.setFileUrl( arg0.getFileUrl() );
        arg1.setIsDefault( arg0.getIsDefault() );
        arg1.setOriginalName( arg0.getOriginalName() );
        arg1.setOssId( arg0.getOssId() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setResumeId( arg0.getResumeId() );
        arg1.setResumeName( arg0.getResumeName() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
