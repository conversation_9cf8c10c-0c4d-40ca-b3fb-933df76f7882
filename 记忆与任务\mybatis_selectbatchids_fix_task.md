# 任务: 修复MyBatis-Plus selectBatchIds过时方法警告

**创建时间**: 2025-01-08 
**任务类型**: 快速修复

## 任务描述
修复项目中MyBatis-Plus的`selectBatchIds`方法已过时的编译警告，涉及以下文件：
1. InterviewServiceImpl.java:1455,1465
2. JobServiceImpl.java:161
3. QuestionServiceImpl.java:207
4. QuestionCommentServiceImpl.java:155

错误信息：`java: com.baomidou.mybatisplus.core.mapper.BaseMapper中的selectBatchIds(java.util.Collection<? extends java.io.Serializable>)已过时`

## 修复方案
直接将所有`selectBatchIds`替换为`selectByIds`，功能完全相同。

## 执行结果
✅ **修复完成**: 5处调用全部修复
✅ **编译状态**: 无警告
✅ **功能验证**: 零风险，方法功能完全相同

## 修复详情
1. **InterviewServiceImpl.java:1455** - `jobMapper.selectBatchIds(jobIds)` → `jobMapper.selectByIds(jobIds)`
2. **InterviewServiceImpl.java:1465** - `jobCategoryMapper.selectBatchIds(categoryIds)` → `jobCategoryMapper.selectByIds(categoryIds)`
3. **JobServiceImpl.java:161** - `jobCategoryMapper.selectBatchIds(categoryIds)` → `jobCategoryMapper.selectByIds(categoryIds)`
4. **QuestionServiceImpl.java:207** - `baseMapper.selectBatchIds(ids)` → `baseMapper.selectByIds(ids)`
5. **QuestionCommentServiceImpl.java:155** - `baseMapper.selectBatchIds(ids)` → `baseMapper.selectByIds(ids)`

## 技术价值
- 消除MyBatis-Plus相关编译警告
- 与之前的deleteBatchIds修复形成完整的API更新
- 建立MyBatis-Plus最新API使用规范
- 为项目代码质量提升做出贡献
