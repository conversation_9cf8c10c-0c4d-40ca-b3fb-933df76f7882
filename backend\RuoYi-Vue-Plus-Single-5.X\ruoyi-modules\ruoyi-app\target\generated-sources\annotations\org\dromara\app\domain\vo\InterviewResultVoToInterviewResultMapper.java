package org.dromara.app.domain.vo;

import io.github.linpeilie.AutoMapperConfig__137;
import io.github.linpeilie.BaseMapper;
import org.dromara.app.domain.InterviewResult;
import org.dromara.app.domain.InterviewResultToInterviewResultVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__137.class,
    uses = {InterviewResultToInterviewResultVoMapper.class},
    imports = {}
)
public interface InterviewResultVoToInterviewResultMapper extends BaseMapper<InterviewResultVo, InterviewResult> {
}
