# 任务: 修复多个Java编译警告和错误

**创建时间**: 2025-01-08 

## 任务描述

修复以下Java编译问题：

1. **XunfeiServiceImpl.java**: 大量JSONObject.put方法过时警告（约30个位置）
2. **OllamaConfig.java**: OkHttp3ClientHttpRequestFactory过时警告  
3. **QuestionCommentController.java**: 找不到getUserId()方法的编译错误

## 1. Analysis (RESEARCH)

### 问题分析

**JSONObject.put过时问题**:
- 位置: XunfeiServiceImpl.java 多个位置（143, 166, 562, 615, 635-636, 640, 699, 726, 729-730, 734, 751-756, 761-764, 779-784, 789-792, 804-805, 809-812, 824-825, 829-835）
- 原因: hutool库升级，推荐使用set()方法替代put()方法
- 解决方案: 批量替换put()为set()

**OkHttp3ClientHttpRequestFactory过时问题**:
- 位置: OllamaConfig.java:25
- 原因: Spring框架升级，该类被标记为待删除
- 需要调查: 新的替代方案

**getUserId()方法缺失问题**:
- 位置: QuestionCommentController.java (113, 128, 141行)
- 原因: 可能是基类方法缺失或导入问题
- 需要调查: 该方法应该从哪里来

### 发现的关键信息
- 需要查看具体代码实现
- 需要了解项目的基类结构
- 需要确认Spring和hutool的版本

## 2. Proposed Solutions (INNOVATE)

**方案A**: 分别修复每个问题
- JSONObject.put问题: 批量替换为set()方法
- OkHttp3ClientHttpRequestFactory问题: 替换为HttpComponentsClientHttpRequestFactory
- getUserId()问题: 使用LoginHelper.getUserId()

**推荐方案**: 方案A，因为问题类型不同，需要分别处理。

## 3. Implementation Plan (PLAN)

### Implementation Checklist:
1. [x] 修复XunfeiServiceImpl.java中的JSONObject.put过时警告
2. [x] 修复OllamaConfig.java中的OkHttp3ClientHttpRequestFactory过时警告
3. [x] 修复QuestionCommentController.java中的getUserId()方法缺失问题
4. [ ] 验证修复结果

## 4. Execution & Progress (EXECUTE)

### 进度日志

**[2025-01-08]**
- 步骤: [✔] 修复XunfeiServiceImpl.java中的JSONObject.put过时警告
- 变更: 替换了约30个JSONObject.put()调用为set()调用
- 理由: hutool库升级，推荐使用set()方法
- 修正: 无
- 阻塞: 无
- 状态: 已完成

**[2025-01-08]**
- 步骤: [✔] 修复OllamaConfig.java中的OkHttp3ClientHttpRequestFactory过时警告
- 变更: 替换OkHttp3ClientHttpRequestFactory为HttpComponentsClientHttpRequestFactory
- 理由: Spring框架升级，OkHttp3ClientHttpRequestFactory被标记为待删除
- 修正: 移除了setWriteTimeout调用（新类不支持）
- 阻塞: 无
- 状态: 已完成

**[2025-01-08]**
- 步骤: [✔] 修复QuestionCommentController.java中的getUserId()方法缺失问题
- 变更: 导入LoginHelper类，替换getUserId()为LoginHelper.getUserId()
- 理由: BaseController中没有getUserId()方法，需要使用框架提供的LoginHelper
- 修正: 无
- 阻塞: 无
- 状态: 已完成
