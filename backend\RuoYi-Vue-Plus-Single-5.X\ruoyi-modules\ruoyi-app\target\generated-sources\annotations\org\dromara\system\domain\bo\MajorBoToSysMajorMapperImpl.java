package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.SysMajor;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-01T20:50:00+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.50.v20250729-0351, environment: Java 21.0.8 (Eclipse Adoptium)"
)
@Component
public class MajorBoToSysMajorMapperImpl implements MajorBoToSysMajorMapper {

    @Override
    public SysMajor convert(MajorBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysMajor sysMajor = new SysMajor();

        sysMajor.setCreateBy( arg0.getCreateBy() );
        sysMajor.setCreateDept( arg0.getCreateDept() );
        sysMajor.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysMajor.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysMajor.setSearchValue( arg0.getSearchValue() );
        sysMajor.setUpdateBy( arg0.getUpdateBy() );
        sysMajor.setUpdateTime( arg0.getUpdateTime() );
        sysMajor.setColor( arg0.getColor() );
        sysMajor.setIcon( arg0.getIcon() );
        sysMajor.setMajorCode( arg0.getMajorCode() );
        sysMajor.setMajorId( arg0.getMajorId() );
        sysMajor.setMajorName( arg0.getMajorName() );
        sysMajor.setQuestionBankCount( arg0.getQuestionBankCount() );
        sysMajor.setRemark( arg0.getRemark() );
        sysMajor.setSort( arg0.getSort() );
        sysMajor.setStatus( arg0.getStatus() );

        return sysMajor;
    }

    @Override
    public SysMajor convert(MajorBo arg0, SysMajor arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setColor( arg0.getColor() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setMajorCode( arg0.getMajorCode() );
        arg1.setMajorId( arg0.getMajorId() );
        arg1.setMajorName( arg0.getMajorName() );
        arg1.setQuestionBankCount( arg0.getQuestionBankCount() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
